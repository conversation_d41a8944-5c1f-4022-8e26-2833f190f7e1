<template>
  <view class="video-edit-container">
    <!-- 顶部标题区域 -->
    <view class="page-header">
      <view class="header-content">
        <view class="title-section">
          <view class="title-icon">
            <text class="iconfont icon-video"></text>
          </view>
          <view class="title-text">
            <text class="main-title">云剪辑工作台</text>
            <text class="sub-title">智能视频编辑，让创作更简单</text>
          </view>
        </view>
        <view class="header-actions">
          <up-button
            type="primary"
            size="normal"
            @click="handleCreate"
            class="create-btn"
          >
            <text class="iconfont icon-plus"></text>
            <text>创建新工程</text>
          </up-button>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选区域 -->
    <view class="search-section">
      <view class="search-bar">
        <up-search
          v-model="queryParams.keyword"
          placeholder="搜索工程标题或描述..."
          :show-action="false"
          bg-color="#f5f5f5"
          border-color="transparent"
        />
      </view>
      <view class="filter-bar">
        <up-dropdown ref="dropdown">
          <up-dropdown-item
            v-model="queryParams.status"
            title="状态筛选"
            :options="statusOptions"
          />
        </up-dropdown>
      </view>
    </view>

    <!-- 项目列表 -->
    <scroll-view
      class="project-list-container"
      scroll-y="true"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
    >
      <!-- 加载状态 -->
      <view
        v-if="loading && projectList.length === 0"
        class="loading-container"
      >
        <up-loading-icon mode="spinner" size="40" color="#409eff" />
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 项目列表 -->
      <view v-else-if="projectList.length > 0" class="project-list">
        <view
          v-for="(item, index) in projectList"
          :key="item.ProjectId"
          class="project-item"
          @click="handleEdit(item)"
        >
          <!-- 项目卡片 -->
          <view class="project-card">
            <!-- 左侧预览图 -->
            <view class="project-preview">
              <image
                v-if="item.CoverURL"
                :src="item.CoverURL"
                class="project-thumbnail"
                mode="aspectFill"
              />
              <view v-else class="thumbnail-placeholder">
                <text class="iconfont icon-video"></text>
              </view>
            </view>

            <!-- 中间内容区域 -->
            <view class="project-content">
              <view class="project-title">{{ item.Title }}</view>
              <view v-if="item.Description" class="project-description">{{
                item.Description
              }}</view>
              <view class="project-meta">
                <text class="project-id">ID: {{ item.ProjectId }}</text>
                <text class="project-time">{{
                  formatTime(item.ModifiedTime)
                }}</text>
              </view>
            </view>

            <!-- 右侧状态和操作 -->
            <view class="project-actions">
              <up-tag
                :text="getStatusConfig(item.Status).label"
                :type="getStatusConfig(item.Status).type"
                size="mini"
                class="status-tag"
              />
              <view class="action-buttons">
                <up-button
                  type="primary"
                  size="mini"
                  @click.stop="handleEdit(item)"
                  class="edit-btn"
                >
                  编辑
                </up-button>
                <up-button
                  type="error"
                  size="mini"
                  @click.stop="handleDelete(item)"
                  class="delete-btn"
                >
                  删除
                </up-button>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading" class="empty-container">
        <view class="empty-icon">
          <text class="iconfont icon-empty"></text>
        </view>
        <text class="empty-text">暂无工程数据</text>
        <up-button
          type="primary"
          @click="handleCreate"
          class="empty-create-btn"
        >
          创建第一个工程
        </up-button>
      </view>

      <!-- 加载更多状态 -->
      <view v-if="loadingMore" class="loading-more">
        <up-loading-icon mode="spinner" size="24" color="#999" />
        <text class="loading-more-text">加载更多...</text>
      </view>

      <!-- 没有更多数据 -->
      <view v-if="noMoreData && projectList.length > 0" class="no-more-data">
        <text>没有更多数据了</text>
      </view>
    </scroll-view>
    <up-loadmore
      :status="status"
      :loading-text="loadingText"
      :loadmore-text="loadmoreText"
      :nomore-text="nomoreText"
      dashed
      line
      v-if="taskList.length > 0"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from "vue";
import {
  listEditingProjects,
  deleteEditingProjects,
  type ProjectInfo,
} from "@/api/platform/videoEdit";
import { parseTime } from "@/utils/ruoyi";


const status = ref("loadmore");
const loadmoreText = ref("轻轻上拉");
const loadingText = ref("努力加载中");
const nomoreText = ref("实在没有了");


const total = ref(0);
const taskList = ref<ProjectInfo[]>([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: "",
  status: undefined,
});


// 页面加载时获取数据
onMounted(() => {
  getTaskList();
});

// 获取项目列表
const getTaskList = async () => {
  try {
    status.value = "loading";
    const res = await listEditingProjects(queryParams.value);
    console.log("获取到的列表数据:", res);
    const allProjects = res.data.ProjectList || [];
    total.value = allProjects.length;

    if (queryParams.value.pageNum === 1) {
      taskList.value = allProjects;
    } else {
      taskList.value.push(...allProjects);
    }
  } catch (error) {
    console.log("出现错误:", error);
  } finally {
    console.log("获取列表完成");
    status.value = "loadmore";
    uni.stopPullDownRefresh();
  }
};

// 响应式数据
const loading = ref(true);
const loadingMore = ref(false);
const refreshing = ref(false);
const noMoreData = ref(false);
const projectList = ref<ProjectInfo[]>([]);

// // 查询参数
// const queryParams = reactive<VideoEditListParams>({
//   pageNum: 1,
//   pageSize: 10,
//   keyword: "",
//   status: undefined,
// });

// 状态配置
const statusConfig = {
  Draft: { label: "草稿", type: "info" },
  Editing: { label: "编辑中", type: "primary" },
  Producing: { label: "制作中", type: "warning" },
  Produced: { label: "已制作完成", type: "success" },
  ProduceFailed: { label: "制作失败", type: "error" },
  Normal: { label: "正常", type: "success" },
  default: { label: "未知", type: "info" },
};

// 状态选项
const statusOptions = computed(() => [
  { label: "全部", value: "" },
  { label: "草稿", value: "Draft" },
  { label: "编辑中", value: "Editing" },
  { label: "制作中", value: "Producing" },
  { label: "已制作完成", value: "Produced" },
  { label: "制作失败", value: "ProduceFailed" },
  { label: "正常", value: "Normal" },
]);

// 获取状态配置
function getStatusConfig(status: string) {
  return (
    statusConfig[status as keyof typeof statusConfig] || statusConfig.default
  );
}

// 格式化时间
function formatTime(timeStr: string) {
  if (!timeStr) return "";

  try {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // 小于1分钟
    if (diff < 60 * 1000) {
      return "刚刚";
    }
    // 小于1小时
    if (diff < 60 * 60 * 1000) {
      return `${Math.floor(diff / (60 * 1000))}分钟前`;
    }
    // 小于1天
    if (diff < 24 * 60 * 60 * 1000) {
      return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
    }
    // 大于1天，使用parseTime格式化
    return parseTime(timeStr, "{y}-{m}-{d} {h}:{i}");
  } catch (error) {
    console.error("时间格式化失败:", error);
    return timeStr;
  }
}

// 加载更多
// function loadMore() {
//   if (loadingMore.value || noMoreData.value) return;

//   queryParams.pageNum = (queryParams.pageNum || 1) + 1;
//   getProjectList(true);
// }

// 下拉刷新
// function onRefresh() {
//   refreshing.value = true;
//   queryParams.pageNum = 1;
//   noMoreData.value = false;
//   getProjectList();
// }

// // 搜索
// function handleSearch() {
//   queryParams.pageNum = 1;
//   noMoreData.value = false;
//   getProjectList();
// }

// // 清空搜索
// function handleClear() {
//   queryParams.keyword = "";
//   handleSearch();
// }

// 状态筛选变化
// function handleStatusChange() {
//   queryParams.pageNum = 1;
//   noMoreData.value = false;
//   getProjectList();
// }

// 创建工程 - 跳转到视频剪辑页面
function handleCreate() {
  uni.navigateTo({
    url: "/pages_workbench/pages/videoEdit/index?from=work",
    success: (res) => {
      console.log("跳转成功:", res);
    },
    fail: (err) => {
      console.error("跳转失败:", err);
      uni.showToast({
        title: `跳转失败: ${err.errMsg}`,
        icon: "none",
      });
    },
  });
}

// 编辑工程 - 跳转到视频剪辑页面
function handleEdit(item: ProjectInfo) {
  uni.navigateTo({
    url: `/pages/index?from=work&projectId=${
      item.ProjectId
    }&title=${encodeURIComponent(item.Title)}`,
  });
}

// 删除工程
async function handleDelete(item: ProjectInfo) {
  try {
    const result = await uni.showModal({
      title: "确认删除",
      content: `确定要删除工程"${item.Title}"吗？`,
      confirmText: "删除",
      confirmColor: "#ff4757",
    });

    if (!result.confirm) return;

    uni.showLoading({
      title: "删除中...",
    });

    await deleteEditingProjects([item.ProjectId]);

    uni.hideLoading();
    uni.showToast({
      title: "删除成功",
      icon: "success",
    });

    // getProjectList(); // 刷新列表
  } catch (error) {
    uni.hideLoading();
    console.error("删除失败:", error);
    uni.showToast({
      title: "删除失败",
      icon: "error",
    });
  }
}

// 页面显示时刷新数据（从其他页面返回时）
// uni.$on("refreshWorkList", () => {
//   getProjectList();
// });

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off("refreshWorkList");
});
</script>

<style lang="scss" scoped>
.video-edit-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  // 顶部标题区域
  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20rpx 30rpx;
    padding-top: calc(var(--status-bar-height) + 20rpx);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        display: flex;
        align-items: center;

        .title-icon {
          width: 60rpx;
          height: 60rpx;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;

          .iconfont {
            font-size: 32rpx;
            color: #fff;

            &.icon-video:before {
              content: "\e7c0";
            }
          }
        }

        .title-text {
          display: flex;
          flex-direction: column;

          .main-title {
            font-size: 36rpx;
            font-weight: bold;
            color: #fff;
            line-height: 1.2;
          }

          .sub-title {
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 4rpx;
          }
        }
      }

      .header-actions {
        .create-btn {
          background: rgba(255, 255, 255, 0.2);
          border: 1rpx solid rgba(255, 255, 255, 0.3);
          border-radius: 50rpx;
          padding: 16rpx 24rpx;
          display: flex;
          align-items: center;

          .iconfont {
            font-size: 24rpx;
            color: #fff;
            margin-right: 8rpx;

            &.icon-plus:before {
              content: "\e6ef";
            }
          }

          text {
            color: #fff;
            font-size: 28rpx;
          }
        }
      }
    }
  }

  // 搜索筛选区域
  .search-section {
    background-color: #fff;
    padding: 20rpx 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .search-bar {
      margin-bottom: 20rpx;
    }

    .filter-bar {
      display: flex;
      align-items: center;
    }
  }

  // 项目列表容器
  .project-list-container {
    flex: 1;
    padding: 20rpx 30rpx;

    // 加载状态
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;

      .loading-text {
        margin-top: 20rpx;
        color: #999;
        font-size: 28rpx;
      }
    }

    // 项目列表
    .project-list {
      .project-item {
        margin-bottom: 20rpx;

        .project-card {
          background-color: #fff;
          border-radius: 16rpx;
          padding: 24rpx;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
          display: flex;
          align-items: flex-start;
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.98);
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
          }

          // 左侧预览图
          .project-preview {
            width: 120rpx;
            height: 120rpx;
            border-radius: 12rpx;
            overflow: hidden;
            margin-right: 24rpx;
            flex-shrink: 0;

            .project-thumbnail {
              width: 100%;
              height: 100%;
            }

            .thumbnail-placeholder {
              width: 100%;
              height: 100%;
              background-color: #f0f0f0;
              display: flex;
              align-items: center;
              justify-content: center;

              .iconfont {
                font-size: 48rpx;
                color: #ccc;

                &.icon-video:before {
                  content: "\e7c0";
                }
              }
            }
          }

          // 中间内容区域
          .project-content {
            flex: 1;
            margin-right: 20rpx;

            .project-title {
              font-size: 32rpx;
              font-weight: 600;
              color: #333;
              line-height: 1.4;
              margin-bottom: 8rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .project-description {
              font-size: 26rpx;
              color: #666;
              line-height: 1.4;
              margin-bottom: 12rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
            }

            .project-meta {
              display: flex;
              flex-direction: column;
              gap: 4rpx;

              .project-id {
                font-size: 22rpx;
                color: #999;
              }

              .project-time {
                font-size: 22rpx;
                color: #999;
              }
            }
          }

          // 右侧状态和操作
          .project-actions {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 12rpx;

            .status-tag {
              margin-bottom: 8rpx;
            }

            .action-buttons {
              display: flex;
              gap: 8rpx;

              .edit-btn,
              .delete-btn {
                padding: 8rpx 16rpx;
                font-size: 22rpx;
                border-radius: 8rpx;
              }
            }
          }
        }
      }
    }

    // 空状态
    .empty-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;

      .empty-icon {
        width: 120rpx;
        height: 120rpx;
        background-color: #f0f0f0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24rpx;

        .iconfont {
          font-size: 60rpx;
          color: #ccc;

          &.icon-empty:before {
            content: "\e69a";
          }
        }
      }

      .empty-text {
        font-size: 28rpx;
        color: #999;
        margin-bottom: 40rpx;
      }

      .empty-create-btn {
        padding: 16rpx 32rpx;
        border-radius: 50rpx;
      }
    }

    // 加载更多
    .loading-more {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 0;

      .loading-more-text {
        margin-left: 16rpx;
        font-size: 26rpx;
        color: #999;
      }
    }

    // 没有更多数据
    .no-more-data {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 0;

      text {
        font-size: 26rpx;
        color: #999;
      }
    }
  }
}
</style>
